rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    function isAuthenticated() {
      return request.auth != null;
    }
    
    function isOwner(userId) {
      return request.auth.uid == userId;
    }
    
    function isValidSignal() {
      let requiredFields = [
        'description',
        'imageUrl',
        'location',
        'userId',
        'userEmail',
        'status',
        'createdAt',
        'updatedAt'
      ];
      let hasAllFields = requiredFields.hasAll(request.resource.data.keys());
      let validLocation = request.resource.data.location.latitude is number 
                        && request.resource.data.location.longitude is number;
      let validStatus = request.resource.data.status in ['pending', 'in_progress', 'resolved'];
      let validImageUrl = request.resource.data.imageUrl.matches('^data:image/.*base64,.*');
      return hasAllFields && validLocation && validStatus && validImageUrl;
    }
    
    match /signals/{signalId} {
      allow create: if isAuthenticated() 
                   && isOwner(request.resource.data.userId)
                   && isValidSignal();
      allow read: if isAuthenticated() 
                 && isOwner(resource.data.userId);
      allow update: if isAuthenticated() 
                   && (isOwner(resource.data.userId) 
                       && request.resource.data.diff(resource.data).affectedKeys()
                         .hasOnly(['description']));
      allow delete: if false; // Disable delete for now
    }
    
    match /userStats/{userId} {
      allow read: if isAuthenticated() && isOwner(userId);
      allow write: if isAuthenticated() && isOwner(userId); // Allow owner to update stats
    }
  }
}